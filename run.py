# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess
from model_path_utils import get_roberta_model_path

def run_step1(dataset='iemocap', model_type='bilstm', use_audio=True, use_visual=True, 
              batch_size=8, epochs=30, learning_rate=1e-5):
    """运行Step1训练"""
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate)
    ]
    
    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    
    print(f"运行命令: {' '.join(cmd)}")
    subprocess.run(cmd)

def run_step2(dataset='iemocap', model_type='bilstm', use_audio=True, use_visual=True,
              use_emotion_category=False, batch_size=32, epochs=12, learning_rate=0.005):
    """运行Step2训练"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate)
    ]
    
    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')
    
    print(f"运行命令: {' '.join(cmd)}")
    subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch 运行脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'], 
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'], 
                       default='iemocap', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'], 
                       default='bert', help='模型类型')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=False,
                       help='使用情感类别（Step2）')
    
    args = parser.parse_args()
    
    print("=== MECPE PyTorch 训练 ===")
    
    # 检查本地模型路径
    if args.model_type == 'bert':
        model_path = get_roberta_model_path()
        print(f"本地模型路径: {model_path}")
        if not os.path.exists(model_path):
            print("❌ 错误: 找不到本地RoBERTa模型!")
            print("请确保roberta文件夹位于MECPE_pytorch的同级目录下")
            print("或运行 python test_model_loading.py 进行诊断")
            return
        else:
            print("✓ 本地模型路径检查通过")
    
    print(f"数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"使用音频: {args.use_audio}")
    print(f"使用视觉: {args.use_visual}")
    
    if args.step in ['step1', 'both']:
        print("\n=== 开始Step1训练 ===")
        if args.model_type == 'bert':
            run_step1(args.dataset, args.model_type, args.use_audio, args.use_visual,
                     batch_size=8, epochs=15, learning_rate=1e-5)
        else:
            run_step1(args.dataset, args.model_type, args.use_audio, args.use_visual,
                     batch_size=128, epochs=30, learning_rate=0.005)
    
    if args.step in ['step2', 'both']:
        print("\n=== 开始Step2训练 ===")
        if args.model_type == 'bert':
            run_step2(args.dataset, args.model_type, args.use_audio, args.use_visual,
                     args.use_emotion_category, batch_size=32, epochs=12, learning_rate=1e-5)
        else:
            run_step2(args.dataset, args.model_type, args.use_audio, args.use_visual,
                     args.use_emotion_category, batch_size=256, epochs=12, learning_rate=0.005)
    
    print("\n=== 训练完成 ===")

if __name__ == "__main__":
    main()
