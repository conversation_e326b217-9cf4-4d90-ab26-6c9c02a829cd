# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from transformers import AutoModel, AutoConfig
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_path_utils import ensure_model_path_exists


class BiLSTMEncoder(nn.Module):
    """双向LSTM编码器"""
    
    def __init__(self, input_dim, hidden_dim, dropout=0.1, num_layers=1):
        super(BiLSTMEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        # 只有在多层时才使用dropout
        lstm_dropout = dropout if num_layers > 1 else 0
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers=num_layers, 
                           batch_first=True, bidirectional=True, dropout=lstm_dropout)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, lengths=None):
        """
        Args:
            x: (batch_size, seq_len, input_dim)
            lengths: (batch_size,) 实际序列长度
        Returns:
            output: (batch_size, seq_len, hidden_dim * 2)
        """
        if lengths is not None:
            # 按长度打包序列以提高效率
            packed = nn.utils.rnn.pack_padded_sequence(
                x, lengths.cpu(), batch_first=True, enforce_sorted=False
            )
            output, (h_n, c_n) = self.lstm(packed)
            output, _ = nn.utils.rnn.pad_packed_sequence(output, batch_first=True)
        else:
            output, (h_n, c_n) = self.lstm(x)
        
        return self.dropout(output)


class AttentionLayer(nn.Module):
    """注意力层"""
    
    def __init__(self, hidden_dim):
        super(AttentionLayer, self).__init__()
        self.hidden_dim = hidden_dim
        self.w1 = nn.Linear(hidden_dim, hidden_dim)
        self.w2 = nn.Linear(hidden_dim, 1)
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, x, mask=None):
        """
        Args:
            x: (batch_size, seq_len, hidden_dim)
            mask: (batch_size, seq_len) 1表示有效位置，0表示padding
        Returns:
            attended: (batch_size, hidden_dim)
            attention_weights: (batch_size, seq_len)
        """
        # 计算注意力权重
        u = self.tanh(self.w1(x))  # (batch_size, seq_len, hidden_dim)
        scores = self.w2(u).squeeze(-1)  # (batch_size, seq_len)
        
        # 应用mask
        if mask is not None:
            # 确保mask和scores的维度匹配
            seq_len = scores.size(-1)
            if mask.size(-1) != seq_len:
                # 如果mask长度不匹配，截断或填充
                if mask.size(-1) > seq_len:
                    mask = mask[:, :seq_len]
                else:
                    # 用0填充（表示padding位置）
                    batch_size = mask.size(0)
                    padded_mask = torch.zeros(batch_size, seq_len, device=mask.device, dtype=mask.dtype)
                    padded_mask[:, :mask.size(-1)] = mask
                    mask = padded_mask
            
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = self.softmax(scores)  # (batch_size, seq_len)
        
        # 加权求和
        attended = torch.sum(x * attention_weights.unsqueeze(-1), dim=1)  # (batch_size, hidden_dim)
        
        return attended, attention_weights


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(MultiHeadAttention, self).__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
    
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        seq_len = query.size(1)
        
        # 线性变换并重塑为多头
        Q = self.w_q(query).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        if mask is not None:
            mask = mask.unsqueeze(1).unsqueeze(1)  # (batch_size, 1, 1, seq_len)
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        attended = torch.matmul(attention_weights, V)
        
        # 重新整合多头
        attended = attended.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        return self.w_o(attended)


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model, max_len=512):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len).unsqueeze(1).float()
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           -(math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
    
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(TransformerBlock, self).__init__()
        
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
    
    def forward(self, x, mask=None):
        # 自注意力
        attended = self.attention(x, x, x, mask)
        x = self.norm1(x + attended)
        
        # 前馈网络
        ff_out = self.feed_forward(x)
        x = self.norm2(x + ff_out)
        
        return x


class BERTEncoder(nn.Module):
    """BERT编码器封装"""
    
    def __init__(self, model_name='../roberta', hidden_dim=768, 
                 dropout=0.1, freeze_bert=False):
        super(BERTEncoder, self).__init__()
        
        model_name = ensure_model_path_exists(model_name)
        self.bert = AutoModel.from_pretrained(model_name)
        self.hidden_dim = hidden_dim
        
        if freeze_bert:
            for param in self.bert.parameters():
                param.requires_grad = False
        
        # 如果需要调整维度
        if self.bert.config.hidden_size != hidden_dim:
            self.projection = nn.Linear(self.bert.config.hidden_size, hidden_dim)
        else:
            self.projection = None
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, input_ids, attention_mask=None, output_type='sequence'):
        """
        Args:
            input_ids: (batch_size, seq_len) 或 (batch_size, doc_len, seq_len)
            attention_mask: 对应的attention mask
            output_type: 'sequence' 或 'pooled'
        """
        original_shape = input_ids.shape
        
        # 如果是3D输入，重塑为2D
        if len(original_shape) == 3:
            batch_size, doc_len, seq_len = original_shape
            input_ids = input_ids.view(-1, seq_len)
            if attention_mask is not None:
                attention_mask = attention_mask.view(-1, seq_len)
        
        # BERT编码
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        
        if output_type == 'pooled':
            encoded = outputs.pooler_output  # (batch_size * doc_len, hidden_size)
        else:
            encoded = outputs.last_hidden_state  # (batch_size * doc_len, seq_len, hidden_size)
        
        # 投影到目标维度
        if self.projection is not None:
            encoded = self.projection(encoded)
        
        encoded = self.dropout(encoded)
        
        # 恢复原始形状
        if len(original_shape) == 3:
            if output_type == 'pooled':
                encoded = encoded.view(batch_size, doc_len, -1)
            else:
                encoded = encoded.view(batch_size, doc_len, seq_len, -1)
        
        return encoded


class MultimodalFusion(nn.Module):
    """多模态特征融合"""
    
    def __init__(self, text_dim, audio_dim, visual_dim, hidden_dim, 
                 fusion_type='concat', dropout=0.1):
        super(MultimodalFusion, self).__init__()
        
        self.fusion_type = fusion_type
        self.text_dim = text_dim
        self.audio_dim = audio_dim
        self.visual_dim = visual_dim
        self.hidden_dim = hidden_dim
        
        # 模态特异性投影
        self.text_proj = nn.Linear(text_dim, hidden_dim)
        self.audio_proj = nn.Linear(audio_dim, hidden_dim) 
        self.visual_proj = nn.Linear(visual_dim, hidden_dim)
        
        # 融合策略
        if fusion_type == 'concat':
            self.fusion_layer = nn.Linear(hidden_dim * 3, hidden_dim)
        elif fusion_type == 'attention':
            self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, dropout=dropout)
        elif fusion_type == 'gate':
            self.gate = nn.Linear(hidden_dim * 3, 3)
            
        self.layer_norm = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, text_feat, audio_feat, visual_feat):
        """
        Args:
            text_feat: (batch_size, seq_len, text_dim) 或 (batch_size, text_dim)
            audio_feat: (batch_size, seq_len, audio_dim) 或 (batch_size, audio_dim)
            visual_feat: (batch_size, seq_len, visual_dim) 或 (batch_size, visual_dim)
        """
        # 投影到统一维度
        text_proj = self.text_proj(text_feat)
        audio_proj = self.audio_proj(audio_feat)
        visual_proj = self.visual_proj(visual_feat)
        
        if self.fusion_type == 'concat':
            # 简单拼接
            fused = torch.cat([text_proj, audio_proj, visual_proj], dim=-1)
            fused = self.fusion_layer(fused)
            
        elif self.fusion_type == 'attention':
            # 注意力融合
            modalities = torch.stack([text_proj, audio_proj, visual_proj], dim=-2)  # (..., 3, hidden_dim)
            if len(modalities.shape) == 3:  # (batch_size, 3, hidden_dim)
                modalities = modalities.transpose(0, 1)  # (3, batch_size, hidden_dim)
                fused, _ = self.attention(modalities, modalities, modalities)
                fused = fused.mean(dim=0)  # (batch_size, hidden_dim)
            else:  # (batch_size, seq_len, 3, hidden_dim)
                batch_size, seq_len = modalities.shape[:2]
                modalities = modalities.view(-1, 3, self.hidden_dim).transpose(0, 1)
                fused, _ = self.attention(modalities, modalities, modalities)
                fused = fused.mean(dim=0).view(batch_size, seq_len, self.hidden_dim)
                
        elif self.fusion_type == 'gate':
            # 门控融合
            concat_feat = torch.cat([text_proj, audio_proj, visual_proj], dim=-1)
            gate_weights = F.softmax(self.gate(concat_feat), dim=-1)
            
            modalities = torch.stack([text_proj, audio_proj, visual_proj], dim=-1)
            fused = torch.sum(modalities * gate_weights.unsqueeze(-2), dim=-1)
        
        else:
            # 默认简单平均
            fused = (text_proj + audio_proj + visual_proj) / 3
        
        fused = self.layer_norm(fused)
        fused = self.dropout(fused)
        
        return fused


class FeatureProjection(nn.Module):
    """特征投影层"""
    
    def __init__(self, input_dim, output_dim, activation='relu', dropout=0.1):
        super(FeatureProjection, self).__init__()
        
        self.projection = nn.Linear(input_dim, output_dim)
        
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            self.activation = None
            
        self.layer_norm = nn.LayerNorm(output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        x = self.projection(x)
        if self.activation is not None:
            x = self.activation(x)
        x = self.layer_norm(x)
        x = self.dropout(x)
        return x


class MaskGenerator(nn.Module):
    """掩码生成器"""
    
    @staticmethod
    def create_padding_mask(lengths, max_len):
        """创建padding掩码"""
        batch_size = lengths.size(0)
        mask = torch.arange(max_len, device=lengths.device).expand(batch_size, max_len) < lengths.unsqueeze(1)
        return mask
    
    @staticmethod
    def create_causal_mask(seq_len):
        """创建因果掩码（下三角矩阵）"""
        mask = torch.tril(torch.ones(seq_len, seq_len))
        return mask.bool()


if __name__ == "__main__":
    # 测试各个组件
    batch_size, seq_len, hidden_dim = 4, 10, 256
    
    # 测试BiLSTM
    print("测试BiLSTM编码器...")
    x = torch.randn(batch_size, seq_len, 300)
    lengths = torch.tensor([10, 8, 6, 9])
    
    bilstm = BiLSTMEncoder(300, 128)
    output = bilstm(x, lengths)
    print(f"BiLSTM输出形状: {output.shape}")
    
    # 测试注意力
    print("\n测试注意力层...")
    attention = AttentionLayer(256)
    mask = MaskGenerator.create_padding_mask(lengths, seq_len)
    attended, weights = attention(output, mask)
    print(f"注意力输出形状: {attended.shape}")
    print(f"注意力权重形状: {weights.shape}")
    
    # 测试多模态融合
    print("\n测试多模态融合...")
    text_feat = torch.randn(batch_size, 256)
    audio_feat = torch.randn(batch_size, 100)
    visual_feat = torch.randn(batch_size, 100)
    
    fusion = MultimodalFusion(256, 100, 100, 256, fusion_type='attention')
    fused = fusion(text_feat, audio_feat, visual_feat)
    print(f"融合特征形状: {fused.shape}")
    
    print("\n所有组件测试完成！")
