# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
from .components import (
    BiLSTMEncoder, AttentionLayer, BERTEncoder, 
    MultimodalFusion, FeatureProjection
)


class MECPE_Step2_Model(nn.Module):
    """
    MECPE Step2模型：情感-原因对提取
    输入情感话语和原因话语，预测它们是否构成情感-原因对
    """
    
    def __init__(self, config):
        super(MECPE_Step2_Model, self).__init__()
        
        self.config = config
        self.use_audio = config.use_audio
        self.use_visual = config.use_visual
        self.use_emotion_category = config.use_emotion_category
        self.hidden_dim = config.hidden_dim
        
        # 文本编码器
        if config.model_type == 'bert':
            self.text_encoder = BERTEncoder(
                model_name=config.bert_model_name,
                hidden_dim=self.hidden_dim,
                dropout=config.dropout
            )
        else:
            # 词嵌入
            self.word_embedding = nn.Embedding(
                config.vocab_size, config.embedding_dim, padding_idx=0
            )
            self.embedding_dropout = nn.Dropout(config.dropout)
            
            # BiLSTM编码器
            self.text_encoder = BiLSTMEncoder(
                config.embedding_dim, self.hidden_dim // 2, config.dropout
            )
            
            # 话语级注意力
            self.utterance_attention = AttentionLayer(self.hidden_dim)
        
        # 多模态特征处理
        if self.use_audio:
            self.audio_projection = FeatureProjection(
                config.audio_dim, self.hidden_dim, dropout=config.dropout
            )
        
        if self.use_visual:
            self.visual_projection = FeatureProjection(
                config.visual_dim, self.hidden_dim, dropout=config.dropout
            )
        
        # 位置编码（距离特征）
        self.distance_embedding = nn.Embedding(201, config.position_embedding_dim)  # -100到100的距离
        
        # 情感类别编码（如果使用）
        if self.use_emotion_category:
            self.emotion_embedding = nn.Embedding(config.n_emotions, config.position_embedding_dim)
        
        # 特征融合
        pair_feature_dim = self.hidden_dim * 2  # 情感和原因话语特征
        
        if self.use_audio:
            pair_feature_dim += self.hidden_dim * 2  # 情感和原因的音频特征
        
        if self.use_visual:
            pair_feature_dim += self.hidden_dim * 2  # 情感和原因的视觉特征
        
        pair_feature_dim += config.position_embedding_dim  # 距离特征
        
        if self.use_emotion_category:
            pair_feature_dim += config.position_embedding_dim  # 情感类别特征
        
        # 对分类器
        self.pair_classifier = nn.Sequential(
            nn.Dropout(config.dropout),
            nn.Linear(pair_feature_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(self.hidden_dim, 2)  # 0: 非情感-原因对, 1: 情感-原因对
        )
    
    def encode_utterance(self, input_ids, attention_mask):
        """编码单个话语"""
        if self.config.model_type == 'bert':
            # BERT编码
            utterance_features = self.text_encoder(
                input_ids, attention_mask, output_type='pooled'
            )  # (batch_size, hidden_dim)
        else:
            # BiLSTM编码
            embedded = self.word_embedding(input_ids)  # (batch_size, seq_len, embedding_dim)
            embedded = self.embedding_dropout(embedded)
            
            # 计算实际长度
            lengths = attention_mask.sum(dim=-1)  # (batch_size,)
            # 确保长度至少为1（避免pack_padded_sequence错误）
            lengths = torch.clamp(lengths, min=1)
            
            # BiLSTM编码
            encoded = self.text_encoder(embedded, lengths)  # (batch_size, seq_len, hidden_dim)
            
            # 话语级注意力
            utterance_features, _ = self.utterance_attention(encoded, attention_mask)
        
        return utterance_features
    
    def forward(self, emo_input_ids, emo_attention_mask, cause_input_ids, cause_attention_mask,
                emo_audio, emo_visual, cause_audio, cause_visual, distance, emotion_category=None):
        """
        Args:
            emo_input_ids: (batch_size, max_sen_len) 情感话语的token ids
            emo_attention_mask: (batch_size, max_sen_len) 情感话语的attention mask
            cause_input_ids: (batch_size, max_sen_len) 原因话语的token ids  
            cause_attention_mask: (batch_size, max_sen_len) 原因话语的attention mask
            emo_audio: (batch_size, audio_dim) 情感话语的音频特征
            emo_visual: (batch_size, visual_dim) 情感话语的视觉特征
            cause_audio: (batch_size, audio_dim) 原因话语的音频特征
            cause_visual: (batch_size, visual_dim) 原因话语的视觉特征
            distance: (batch_size,) 话语间的距离
            emotion_category: (batch_size,) 情感类别（可选）
        """
        # 编码情感和原因话语
        emo_text_feat = self.encode_utterance(emo_input_ids, emo_attention_mask)
        cause_text_feat = self.encode_utterance(cause_input_ids, cause_attention_mask)
        
        # 构建特征向量列表
        features = [emo_text_feat, cause_text_feat]
        
        # 处理多模态特征
        if self.use_audio:
            emo_audio_feat = self.audio_projection(emo_audio)
            cause_audio_feat = self.audio_projection(cause_audio)
            features.extend([emo_audio_feat, cause_audio_feat])
        
        if self.use_visual:
            emo_visual_feat = self.visual_projection(emo_visual)
            cause_visual_feat = self.visual_projection(cause_visual)
            features.extend([emo_visual_feat, cause_visual_feat])
        
        # 距离特征
        # 将距离映射到[0, 200]范围，其中100表示距离为0
        distance_indices = torch.clamp(distance + 100, 0, 200)
        distance_feat = self.distance_embedding(distance_indices)
        features.append(distance_feat)
        
        # 情感类别特征（如果使用）
        if self.use_emotion_category and emotion_category is not None:
            emotion_feat = self.emotion_embedding(emotion_category)
            features.append(emotion_feat)
        
        # 拼接所有特征
        pair_features = torch.cat(features, dim=-1)
        
        # 分类
        logits = self.pair_classifier(pair_features)
        
        return {
            'logits': logits,  # (batch_size, 2)
            'emo_features': emo_text_feat,
            'cause_features': cause_text_feat,
            'pair_features': pair_features
        }


class PairDataCollator:
    """Step2的数据整理器，用于处理负采样"""
    
    def __init__(self, negative_sampling_ratio=1.0):
        self.negative_sampling_ratio = negative_sampling_ratio
    
    def __call__(self, batch):
        """
        对批次数据进行负采样平衡
        """
        positive_samples = [item for item in batch if item['label'] == 1]
        negative_samples = [item for item in batch if item['label'] == 0]
        
        # 负采样
        if len(negative_samples) > len(positive_samples) * self.negative_sampling_ratio:
            import random
            negative_samples = random.sample(
                negative_samples, 
                int(len(positive_samples) * self.negative_sampling_ratio)
            )
        
        # 合并样本
        balanced_batch = positive_samples + negative_samples
        
        # 构建批次张量
        result = {}
        for key in balanced_batch[0].keys():
            if key in ['conversation_id']:
                result[key] = [item[key] for item in balanced_batch]
            else:
                result[key] = torch.stack([item[key] for item in balanced_batch])
        
        return result


class Step2Config:
    """Step2模型配置"""
    
    def __init__(self, **kwargs):
        # 模型架构
        self.model_type = kwargs.get('model_type', 'bilstm')  # 'bilstm' or 'bert'
        self.hidden_dim = kwargs.get('hidden_dim', 200)
        self.dropout = kwargs.get('dropout', 0.1)
        
        # 数据维度
        self.vocab_size = kwargs.get('vocab_size', 30000)
        self.embedding_dim = kwargs.get('embedding_dim', 300)
        self.audio_dim = kwargs.get('audio_dim', 100)
        self.visual_dim = kwargs.get('visual_dim', 100)
        self.position_embedding_dim = kwargs.get('position_embedding_dim', 50)
        self.max_sen_len = kwargs.get('max_sen_len', 35)
        
        # 模态选择
        self.use_audio = kwargs.get('use_audio', True)
        self.use_visual = kwargs.get('use_visual', True)
        self.use_emotion_category = kwargs.get('use_emotion_category', True)
        
        # BERT相关
        self.bert_model_name = kwargs.get('bert_model_name', '../roberta')
        
        # 情感类别数
        self.n_emotions = kwargs.get('n_emotions', 7)


class Step2Loss(nn.Module):
    """Step2的损失函数"""
    
    def __init__(self, weight=None, pos_weight=None):
        super(Step2Loss, self).__init__()
        self.criterion = nn.CrossEntropyLoss(weight=weight)
        self.pos_weight = pos_weight
    
    def forward(self, logits, labels):
        """
        Args:
            logits: (batch_size, 2)
            labels: (batch_size,)
        """
        if self.pos_weight is not None:
            # 使用带权重的BCE损失处理类别不平衡
            bce_criterion = nn.BCEWithLogitsLoss(pos_weight=self.pos_weight)
            # 将标签转换为one-hot
            labels_onehot = F.one_hot(labels.long(), num_classes=2).float()
            return bce_criterion(logits, labels_onehot)
        else:
            return self.criterion(logits, labels.long())


if __name__ == "__main__":
    # 测试Step2模型
    print("测试MECPE Step2模型...")
    
    # 配置
    config = Step2Config(
        model_type='bilstm',
        hidden_dim=200,
        vocab_size=10000,
        embedding_dim=300,
        audio_dim=100,
        visual_dim=100,
        use_audio=True,
        use_visual=True,
        use_emotion_category=True,
        n_emotions=6
    )
    
    # 创建模型
    model = MECPE_Step2_Model(config)
    
    # 测试数据
    batch_size, max_sen_len = 4, 20
    
    emo_input_ids = torch.randint(1, 1000, (batch_size, max_sen_len))
    emo_attention_mask = torch.ones(batch_size, max_sen_len)
    cause_input_ids = torch.randint(1, 1000, (batch_size, max_sen_len))
    cause_attention_mask = torch.ones(batch_size, max_sen_len)
    emo_audio = torch.randn(batch_size, 100)
    emo_visual = torch.randn(batch_size, 100)
    cause_audio = torch.randn(batch_size, 100)
    cause_visual = torch.randn(batch_size, 100)
    distance = torch.tensor([1, -2, 3, 0])
    emotion_category = torch.tensor([1, 3, 2, 4])
    
    # 前向传播
    with torch.no_grad():
        outputs = model(
            emo_input_ids, emo_attention_mask, 
            cause_input_ids, cause_attention_mask,
            emo_audio, emo_visual, cause_audio, cause_visual,
            distance, emotion_category
        )
    
    print("输出形状:")
    for key, value in outputs.items():
        print(f"  {key}: {value.shape}")
    
    # 测试损失函数
    labels = torch.tensor([1, 0, 1, 0])
    criterion = Step2Loss()
    loss = criterion(outputs['logits'], labels)
    print(f"\n损失值: {loss.item()}")
    
    print("\nStep2模型测试完成！")
