# MECPE PyTorch Implementation

基于PyTorch的多模态情感-原因对提取（Multimodal Emotion-Cause Pair Extraction）实现，从原TensorFlow版本转换而来。

## 📁 项目结构

```
MECPE_pytorch/
├── data/                    # 数据目录
│   ├── iemocap/            # IEMOCAP数据集
│   │   ├── train.h5
│   │   └── test.h5
│   └── meld/               # MELD数据集
│       ├── train.h5
│       ├── dev.h5
│       └── test.h5
├── models/                 # 模型定义
│   ├── __init__.py
│   ├── components.py       # 基础组件
│   ├── step1_model.py      # Step1模型
│   └── step2_model.py      # Step2模型
├── checkpoints/            # 模型检查点
├── logs/                   # 训练日志
├── config.py              # 配置文件
├── data_loader.py         # 数据加载器
├── utils.py               # 工具函数
├── train_step1.py         # Step1训练脚本
├── train_step2.py         # Step2训练脚本
├── run.py                 # 快速运行脚本
├── requirements.txt       # 依赖包
└── README.md              # 说明文档
```

## 🚀 快速开始

### 1. 环境安装

```bash
# 创建虚拟环境（推荐）
conda create -n mecpe python=3.8
conda activate mecpe

# 安装依赖
pip install -r requirements.txt

# 或使用conda安装PyTorch
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
pip install transformers h5py scikit-learn tqdm
```

### 2. 模型和数据准备

#### 预训练模型设置
项目需要预训练的RoBERTa/BERT模型。请将下载的模型文件夹命名为`roberta`，并放置在与`MECPE_pytorch`同级的目录下：

```
implement/
├── MECPE_pytorch/     # 项目代码
├── roberta/           # 预训练模型文件夹
│   ├── config.json
│   ├── pytorch_model.bin
│   ├── tokenizer.json
│   ├── tokenizer_config.json
│   └── vocab.txt
└── MECPE-main/        # 原始TensorFlow项目
```

#### 数据文件
确保数据文件已放置在正确的目录结构中：
- IEMOCAP: `data/iemocap/train.h5`, `data/iemocap/test.h5`
- MELD: `data/meld/train.h5`, `data/meld/dev.h5`, `data/meld/test.h5`

### 3. 快速训练

```bash
# 使用默认设置训练（IEMOCAP + BiLSTM）
python run.py

# 指定数据集和模型
python run.py --dataset iemocap --model_type bilstm --step both

# 只训练Step1
python run.py --step step1 --dataset meld --model_type bert

# 只训练Step2
python run.py --step step2 --use_emotion_category
```

### 4. 详细训练

#### Step1: 情感和原因检测

```bash
# BiLSTM模型
python train_step1.py \
    --dataset iemocap \
    --model_type bilstm \
    --batch_size 32 \
    --learning_rate 0.005 \
    --epochs 30 \
    --use_audio \
    --use_visual

# BERT模型
python train_step1.py \
    --dataset iemocap \
    --model_type bert \
    --batch_size 8 \
    --learning_rate 1e-5 \
    --epochs 15 \
    --use_audio \
    --use_visual
```

#### Step2: 情感-原因对提取

```bash
# BiLSTM模型
python train_step2.py \
    --dataset iemocap \
    --model_type bilstm \
    --batch_size 32 \
    --learning_rate 0.005 \
    --epochs 12 \
    --use_audio \
    --use_visual \
    --use_emotion_category

# BERT模型
python train_step2.py \
    --dataset iemocap \
    --model_type bert \
    --batch_size 16 \
    --learning_rate 1e-5 \
    --epochs 12 \
    --use_audio \
    --use_visual
```

## 🏗️ 模型架构

### Step1: 情感和原因检测
- **输入**: 对话序列（文本 + 音频 + 视觉特征）
- **输出**: 每个话语的情感标签和原因标签
- **架构**: 
  - 文本编码器（BiLSTM或BERT）
  - 多模态特征融合
  - 对话级序列编码
  - 情感/原因分类器

### Step2: 情感-原因对提取
- **输入**: 情感话语和原因话语对
- **输出**: 该对是否构成情感-原因对
- **架构**:
  - 话语编码器（BiLSTM或BERT）
  - 多模态特征融合
  - 距离编码
  - 二分类器

## 📊 数据集支持

### IEMOCAP
- **情感类别**: 6类（不含neutral）
- **音频特征**: 100维
- **视觉特征**: 100维
- **训练集**: 120个对话，5,810个话语
- **测试集**: 31个对话，1,623个话语

### MELD
- **情感类别**: 6类（不含neutral）
- **音频特征**: 6373维
- **视觉特征**: 4096维
- **训练集**: 984个对话，9,764个话语
- **验证集**: 110个对话，1,069个话语
- **测试集**: 257个对话，2,519个话语

## ⚙️ 主要参数

### 模型参数
- `--model_type`: 模型类型（bilstm/bert）
- `--hidden_dim`: 隐藏层维度（默认200）
- `--use_audio`: 是否使用音频特征
- `--use_visual`: 是否使用视觉特征
- `--share_encoder`: 情感和原因是否共享编码器

### 训练参数
- `--batch_size`: 批次大小
- `--learning_rate`: 学习率
- `--epochs`: 训练轮数
- `--dropout`: Dropout率
- `--weight_decay`: 权重衰减

## 📈 评估指标

### Step1
- **情感检测**: Precision, Recall, F1-score
- **原因检测**: Precision, Recall, F1-score

### Step2
- **分类指标**: Precision, Recall, F1-score
- **对提取指标**: 基于真实情感-原因对的P/R/F1

## 🔧 自定义配置

可以修改`config.py`中的配置类来自定义模型和训练参数：

```python
# 示例：修改模型维度
config = Step1Config(
    hidden_dim=256,
    n_heads=8,
    dropout=0.2
)
```

## 📁 输出文件

### 检查点
- `checkpoints/best_model.pt`: 最佳模型
- `checkpoints/checkpoint_epoch_*.pt`: 各轮次检查点

### 日志
- `logs/step1_*.log`: Step1训练日志
- `logs/step2_*.log`: Step2训练日志

## 🐛 故障排除

### 模型加载错误
如果遇到`OSError: We couldn't connect to 'https://huggingface.co'`错误：

1. **检查本地模型设置**:
   ```bash
   python test_model_loading.py
   ```

2. **确认目录结构**:
   ```
   implement/
   ├── MECPE_pytorch/     # 项目代码
   ├── roberta/           # 预训练模型文件夹
   └── MECPE-main/        # 原始项目
   ```

3. **检查模型文件完整性**:
   - `config.json` - 模型配置
   - `pytorch_model.bin` 或 `model.safetensors` - 模型权重
   - `tokenizer.json` - 分词器
   - `tokenizer_config.json` - 分词器配置
   - `vocab.txt` - 词汇表

### 内存不足
- 减少`batch_size`
- 使用梯度累积
- 降低`hidden_dim`

### CUDA错误
- 检查CUDA版本兼容性
- 使用`--device cpu`强制使用CPU

### 数据加载错误
- 检查H5文件路径和格式
- 确认数据集结构正确

### 设备错误（CUDA/CPU不匹配）
如果遇到设备不匹配错误：
```
RuntimeError: Expected all tensors to be on the same device
```
- 已修复：确保所有tensor创建时指定正确设备
- 运行 `python test_fixes.py` 验证修复

### 索引超出范围错误
如果遇到embedding索引错误：
```
Assertion `srcIndex < srcSelectDimSize` failed
```
- 已修复：动态获取词汇表大小
- 已修复：正确设置情感类别数（IEMOCAP: 6, MELD: 7）
- 已修复：限制距离特征范围

### 序列长度错误
如果遇到序列长度相关错误：
```
RuntimeError: Length of all samples has to be greater than 0
RuntimeError: The size of tensor a must match the size of tensor b
```
- 已修复：确保所有序列长度>=1（避免pack_padded_sequence错误）
- 已修复：自动处理attention层中的维度不匹配
- 运行 `python test_sequence_fixes.py` 验证修复

### 维度不匹配错误
如果遇到mask和模型输出维度不匹配：
```
IndexError: The shape of the mask [32, 35] at index 1 does not match the shape of the indexed tensor [32, 23, 7] at index 1
IndexError: The shape of the mask [32, 23] at index 1 does not match the shape of the indexed tensor [32, 35, 7] at index 1
```
- 已修复：基于模型实际输出维度创建mask，而非输入维度
- 已修复：自动调整目标标签维度以匹配模型输出
- 已修复：确保音频/视觉情感logits与主要输出维度一致

### 数据类型不匹配错误
如果遇到数据类型不匹配：
```
RuntimeError: mat1 and mat2 must have the same dtype, but got Double and Float
```
- 已修复：在Step2数据加载时强制指定float32类型
- 运行 `python test_dimension_fixes.py` 验证修复
- 运行 `python test_quick_fixes.py` 快速验证最新修复

### 批次序列长度不一致错误
如果遇到张量拼接时的维度错误：
```
RuntimeError: Sizes of tensors must match except in dimension 0. Expected size 23 but got size 22 for tensor number 2 in the list.
```
- 已修复：只收集有效位置的预测结果，避免拼接不同长度的张量
- 运行 `python test_batch_consistency.py` 验证修复

### 多分类指标计算错误
如果遇到sklearn指标计算错误：
```
ValueError: Target is multiclass but average='binary'. Please choose another average setting, one of [None, 'micro', 'macro', 'weighted'].
```
- 已修复：自动检测分类任务类型，多分类任务自动使用macro average
- 情感识别（多分类）使用macro average，原因检测（二分类）使用binary average
- 运行 `python test_metrics_fix.py` 验证修复

## 📝 引用

如果您使用了这个实现，请引用原论文：

```bibtex
@ARTICLE{wang2023multimodal,
  author={Wang, Fanfan and Ding, Zixiang and Xia, Rui and Li, Zhaoyu and Yu, Jianfei},
  journal={IEEE Transactions on Affective Computing}, 
  title={Multimodal Emotion-Cause Pair Extraction in Conversations}, 
  year={2023},
  volume={14},
  number={3},
  pages={1832-1844},
  doi = {10.1109/TAFFC.2022.3226559}
}
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

本项目基于MIT许可证开源。
