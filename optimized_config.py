# -*- coding: utf-8 -*-

"""
优化配置文件 - 针对MELD数据集的SOTA性能调优
"""

import argparse
import os
from config import Config

class OptimizedConfig(Config):
    """优化的配置类，针对MELD数据集调优"""
    
    def _add_arguments(self):
        super()._add_arguments()
        
        # 添加优化相关参数
        self.parser.add_argument('--use_focal_loss', action='store_true', default=False,
                                help='使用Focal Loss处理类别不平衡')
        self.parser.add_argument('--focal_alpha', type=float, default=0.25,
                                help='Focal Loss的alpha参数')
        self.parser.add_argument('--focal_gamma', type=float, default=2.0,
                                help='Focal Loss的gamma参数')
        
        self.parser.add_argument('--label_smoothing', type=float, default=0.0,
                                help='标签平滑参数')
        
        self.parser.add_argument('--step2_pos_weight', type=float, default=10.0,
                                help='Step2正样本权重')
        
        self.parser.add_argument('--use_scheduler', action='store_true', default=True,
                                help='使用学习率调度器')
        self.parser.add_argument('--scheduler_type', type=str, default='cosine',
                                choices=['cosine', 'step', 'plateau'],
                                help='学习率调度器类型')
        
        self.parser.add_argument('--data_augmentation', action='store_true', default=False,
                                help='使用数据增强')
        
    def parse_args(self):
        args = super().parse_args()
        
        # MELD数据集优化配置
        if args.dataset == 'meld':
            if args.model_type == 'bilstm':
                if args.stage == 'step1':
                    # Step1优化配置
                    args.batch_size = 64
                    args.learning_rate = 0.001
                    args.epochs = 50
                    args.dropout = 0.3
                    args.weight_decay = 1e-4
                    args.patience = 10
                    args.gradient_clip = 0.5
                    
                    # 损失权重调整
                    args.emotion_weight = 1.0
                    args.cause_weight = 2.0  # 增加原因检测权重
                    args.audio_emotion_weight = 0.3
                    args.visual_emotion_weight = 0.3
                    
                else:  # step2
                    # Step2优化配置
                    args.batch_size = 128
                    args.learning_rate = 0.0005
                    args.epochs = 30
                    args.dropout = 0.4
                    args.weight_decay = 1e-3
                    args.patience = 8
                    args.gradient_clip = 0.5
                    
                    # 类别不平衡处理
                    args.use_focal_loss = True
                    args.step2_pos_weight = 15.0
                    args.negative_sampling_ratio = 3.0  # 减少负采样比例
                    
            elif args.model_type == 'bert':
                if args.stage == 'step1':
                    args.batch_size = 16
                    args.learning_rate = 2e-5
                    args.epochs = 20
                    args.dropout = 0.2
                    args.weight_decay = 1e-5
                    args.warmup_steps = 1000
                else:  # step2
                    args.batch_size = 32
                    args.learning_rate = 1e-5
                    args.epochs = 15
                    args.dropout = 0.3
                    args.weight_decay = 1e-4
                    args.warmup_steps = 500
                    args.use_focal_loss = True
                    args.step2_pos_weight = 20.0
        
        return args


def get_optimized_step1_config():
    """获取Step1优化配置"""
    return {
        'batch_size': 64,
        'learning_rate': 0.001,
        'epochs': 50,
        'dropout': 0.3,
        'weight_decay': 1e-4,
        'patience': 10,
        'gradient_clip': 0.5,
        'emotion_weight': 1.0,
        'cause_weight': 2.0,
        'audio_emotion_weight': 0.3,
        'visual_emotion_weight': 0.3,
        'use_scheduler': True,
        'scheduler_type': 'cosine',
        'label_smoothing': 0.1
    }


def get_optimized_step2_config():
    """获取Step2优化配置"""
    return {
        'batch_size': 128,
        'learning_rate': 0.0005,
        'epochs': 30,
        'dropout': 0.4,
        'weight_decay': 1e-3,
        'patience': 8,
        'gradient_clip': 0.5,
        'use_focal_loss': True,
        'focal_alpha': 0.25,
        'focal_gamma': 2.0,
        'step2_pos_weight': 15.0,
        'negative_sampling_ratio': 3.0,
        'use_scheduler': True,
        'scheduler_type': 'plateau',
        'label_smoothing': 0.05
    }


if __name__ == "__main__":
    # 测试优化配置
    config = OptimizedConfig()
    args = config.parse_args()
    
    print("=== 优化配置参数 ===")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
