#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的训练脚本 - 针对MELD数据集的SOTA性能调优
"""

import os
import sys
import argparse
import subprocess
import torch
from optimized_config import OptimizedConfig, get_optimized_step1_config, get_optimized_step2_config

def run_optimized_step1(dataset='meld', model_type='bilstm'):
    """运行优化的Step1训练"""
    config = get_optimized_step1_config()
    
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(config['batch_size']),
        '--epochs', str(config['epochs']),
        '--learning_rate', str(config['learning_rate']),
        '--dropout', str(config['dropout']),
        '--weight_decay', str(config['weight_decay']),
        '--patience', str(config['patience']),
        '--gradient_clip', str(config['gradient_clip']),
        '--emotion_weight', str(config['emotion_weight']),
        '--cause_weight', str(config['cause_weight']),
        '--audio_emotion_weight', str(config['audio_emotion_weight']),
        '--visual_emotion_weight', str(config['visual_emotion_weight']),
        '--use_audio',
        '--use_visual'
    ]
    
    print(f"🚀 运行优化的Step1训练...")
    print(f"📊 主要优化点:")
    print(f"   - 降低学习率: {config['learning_rate']}")
    print(f"   - 增加正则化: dropout={config['dropout']}, weight_decay={config['weight_decay']}")
    print(f"   - 增加原因检测权重: {config['cause_weight']}")
    print(f"   - 增加耐心值: {config['patience']}")
    
    subprocess.run(cmd)

def run_optimized_step2(dataset='meld', model_type='bilstm'):
    """运行优化的Step2训练"""
    config = get_optimized_step2_config()
    
    cmd = [
        sys.executable, 'train_step2_optimized.py',  # 使用优化版本的训练脚本
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(config['batch_size']),
        '--epochs', str(config['epochs']),
        '--learning_rate', str(config['learning_rate']),
        '--dropout', str(config['dropout']),
        '--weight_decay', str(config['weight_decay']),
        '--patience', str(config['patience']),
        '--gradient_clip', str(config['gradient_clip']),
        '--negative_sampling_ratio', str(config['negative_sampling_ratio']),
        '--use_audio',
        '--use_visual',
        '--use_emotion_category'
    ]
    
    if config['use_focal_loss']:
        cmd.extend(['--use_focal_loss', 
                   '--focal_alpha', str(config['focal_alpha']),
                   '--focal_gamma', str(config['focal_gamma'])])
    
    print(f"🚀 运行优化的Step2训练...")
    print(f"📊 主要优化点:")
    print(f"   - 降低学习率: {config['learning_rate']}")
    print(f"   - 使用Focal Loss处理类别不平衡")
    print(f"   - 减少负采样比例: {config['negative_sampling_ratio']}")
    print(f"   - 增强正则化: dropout={config['dropout']}")
    
    subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description='MECPE 优化训练脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'], 
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'], 
                       default='meld', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'], 
                       default='bilstm', help='模型类型')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎯 MECPE 优化训练 - 针对SOTA性能调优")
    print("=" * 60)
    print(f"📊 数据集: {args.dataset}")
    print(f"🏗️ 模型类型: {args.model_type}")
    print()
    
    if args.step in ['step1', 'both']:
        print("🔥 Step1 优化策略:")
        print("   ✅ 降低学习率防止过拟合")
        print("   ✅ 增加dropout和权重衰减")
        print("   ✅ 增加原因检测任务权重")
        print("   ✅ 延长早停耐心值")
        print("   ✅ 使用余弦学习率调度")
        print()
        run_optimized_step1(args.dataset, args.model_type)
    
    if args.step in ['step2', 'both']:
        print("🔥 Step2 优化策略:")
        print("   ✅ 使用Focal Loss处理类别不平衡")
        print("   ✅ 减少负采样比例")
        print("   ✅ 增加正样本权重")
        print("   ✅ 使用标签平滑")
        print("   ✅ 更强的正则化")
        print()
        run_optimized_step2(args.dataset, args.model_type)
    
    print("=" * 60)
    print("🎉 优化训练完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
