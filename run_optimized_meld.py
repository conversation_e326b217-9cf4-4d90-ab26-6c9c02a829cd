#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MELD数据集优化训练脚本
基于训练日志分析的针对性优化
"""

import os
import sys
import subprocess

def run_optimized_step1():
    """运行优化的Step1训练"""
    print("🚀 开始Step1优化训练...")
    print("📊 优化策略:")
    print("   ✅ 降低学习率: 0.005 → 0.001 (防止过拟合)")
    print("   ✅ 增加dropout: 0.1 → 0.3 (增强正则化)")
    print("   ✅ 增加权重衰减: 1e-5 → 1e-4")
    print("   ✅ 增加原因检测权重: 1.0 → 2.0")
    print("   ✅ 延长早停耐心: 7 → 12")
    print("   ✅ 减小批次大小: 128 → 64 (提高稳定性)")
    print()
    
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', 'meld',
        '--model_type', 'bilstm',
        '--batch_size', '64',           # 减小批次大小
        '--epochs', '50',               # 增加最大轮数
        '--learning_rate', '0.001',     # 降低学习率
        '--dropout', '0.3',             # 增加dropout
        '--weight_decay', '1e-4',       # 增加权重衰减
        '--patience', '12',             # 增加早停耐心
        '--gradient_clip', '0.5',       # 添加梯度裁剪
        '--cause_weight', '2.0',        # 增加原因检测权重
        '--audio_emotion_weight', '0.3', # 降低辅助损失权重
        '--visual_emotion_weight', '0.3',
        '--use_audio',
        '--use_visual'
    ]
    
    subprocess.run(cmd)

def run_optimized_step2():
    """运行优化的Step2训练"""
    print("🚀 开始Step2优化训练...")
    print("📊 优化策略:")
    print("   ✅ 降低学习率: 0.005 → 0.0005 (更稳定的训练)")
    print("   ✅ 增加dropout: 0.1 → 0.4 (防止过拟合)")
    print("   ✅ 减少负采样比例: 无限制 → 3:1 (缓解类别不平衡)")
    print("   ✅ 使用类别权重处理不平衡")
    print("   ✅ 增加早停耐心: 7 → 10")
    print("   ✅ 减小批次大小: 256 → 128")
    print()
    
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', 'meld',
        '--model_type', 'bilstm',
        '--batch_size', '128',          # 减小批次大小
        '--epochs', '30',               # 适当的训练轮数
        '--learning_rate', '0.0005',    # 更低的学习率
        '--dropout', '0.4',             # 更强的正则化
        '--weight_decay', '1e-3',       # 增加权重衰减
        '--patience', '10',             # 增加早停耐心
        '--gradient_clip', '0.5',       # 梯度裁剪
        '--negative_sampling_ratio', '3.0',  # 限制负采样比例
        '--use_audio',
        '--use_visual',
        '--use_emotion_category'
    ]
    
    subprocess.run(cmd)

def run_bert_optimized():
    """运行BERT模型的优化训练"""
    print("🚀 开始BERT模型优化训练...")
    print("📊 BERT优化策略:")
    print("   ✅ 使用更小的学习率和预热")
    print("   ✅ 适当的批次大小")
    print("   ✅ 更少的训练轮数防止过拟合")
    print()
    
    # Step1
    print("--- Step1 (BERT) ---")
    cmd1 = [
        sys.executable, 'train_step1.py',
        '--dataset', 'meld',
        '--model_type', 'bert',
        '--batch_size', '16',
        '--epochs', '20',
        '--learning_rate', '2e-5',
        '--dropout', '0.2',
        '--weight_decay', '1e-5',
        '--warmup_steps', '1000',
        '--patience', '8',
        '--cause_weight', '1.5',
        '--use_audio',
        '--use_visual'
    ]
    subprocess.run(cmd1)
    
    # Step2
    print("--- Step2 (BERT) ---")
    cmd2 = [
        sys.executable, 'train_step2.py',
        '--dataset', 'meld',
        '--model_type', 'bert',
        '--batch_size', '32',
        '--epochs', '15',
        '--learning_rate', '1e-5',
        '--dropout', '0.3',
        '--weight_decay', '1e-4',
        '--warmup_steps', '500',
        '--patience', '6',
        '--negative_sampling_ratio', '4.0',
        '--use_audio',
        '--use_visual',
        '--use_emotion_category'
    ]
    subprocess.run(cmd2)

def main():
    import argparse
    parser = argparse.ArgumentParser(description='MELD数据集优化训练')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'], 
                       default='bilstm', help='模型类型')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'], 
                       default='both', help='训练步骤')
    
    args = parser.parse_args()
    
    print("=" * 70)
    print("🎯 MELD数据集SOTA性能优化训练")
    print("=" * 70)
    print(f"🏗️ 模型类型: {args.model_type}")
    print(f"📝 训练步骤: {args.step}")
    print()
    
    print("🔍 基于训练日志的问题分析:")
    print("   ❌ Step1过拟合严重 (训练F1=0.9+, 验证F1=0.7)")
    print("   ❌ Step2类别极度不平衡 (召回率仅0.33)")
    print("   ❌ 学习率可能过高导致不稳定")
    print("   ❌ 正则化不足")
    print()
    
    if args.model_type == 'bilstm':
        if args.step in ['step1', 'both']:
            run_optimized_step1()
        if args.step in ['step2', 'both']:
            run_optimized_step2()
    else:  # bert
        run_bert_optimized()
    
    print("=" * 70)
    print("🎉 优化训练完成！")
    print("💡 预期改进:")
    print("   ✅ Step1: 减少过拟合，提高泛化性能")
    print("   ✅ Step2: 提高召回率，改善F1分数")
    print("   ✅ 整体: 更稳定的训练过程")
    print("=" * 70)

if __name__ == "__main__":
    main()
